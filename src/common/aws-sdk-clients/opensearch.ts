import { defaultProvider } from '@aws-sdk/credential-provider-node'
import { Client } from '@opensearch-project/opensearch'
import { AwsSigv4Signer } from '@opensearch-project/opensearch/aws'

// Deprecated: Legacy functional approach
export const opensearchClient = new Client({
  ...AwsSigv4Signer({
    region: 'us-east-1',
    service: 'aoss',
    getCredentials: () => {
      const credentialsProvider = defaultProvider()
      return credentialsProvider()
    },
  }),
  requestTimeout: 30000,
  node: process.env.AOSS_ENDPOINT,
})

// Class approach
type GetCredentials = () => ReturnType<ReturnType<typeof defaultProvider>>

export class OpenSearchServerlessClient {
  public readonly client: Client

  constructor(opts: {
    endpoint: string
    region?: string
    requestTimeoutMs?: number
    getCredentials?: GetCredentials
  }) {
    const { endpoint, region = 'us-east-1', requestTimeoutMs = 30_000, getCredentials } = opts

    if (!endpoint) throw new Error('endpoint is required')

    // Default AWS creds provider chain (works on Lambda, ECS, dev, etc.)
    const credentialsProvider =
      getCredentials ??
      (() => {
        const provider = defaultProvider()
        return provider()
      })

    this.client = new Client({
      ...AwsSigv4Signer({
        region,
        service: 'aoss',
        getCredentials: credentialsProvider,
      }),
      requestTimeout: requestTimeoutMs,
      node: endpoint,
    })
  }
}
