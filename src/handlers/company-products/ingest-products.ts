import { MetricUnit } from '@aws-lambda-powertools/metrics'
import { ProductV2Models } from '@getgreenline/products'
import middy from '@middy/core'
import sqsPartialBatchFailureMiddleware from '@middy/sqs-partial-batch-failure'
import { opensearchClient } from '../../common/aws-sdk-clients/opensearch'
import { logger } from '../../common/powertools/logger'
import { EMetricNames, metrics } from '../../common/powertools/metrics'
import { OpensearchIndex } from '../../models/common/enums'

export interface IProductRawEvent {
  Records: {
    body: string
  }[]
  initialSetup?: boolean
}

export interface IProductParsedEvent {
  source: string
  id: string
  detail: {
    product: ProductV2Models.IProductContract
  }
}

const initialSetup = async (indexName: OpensearchIndex) => {
  const indexExistsRes = await opensearchClient.indices.exists({
    index: indexName,
  })
  const indexExists = indexExistsRes.body.valueOf()
  logger.info(indexExists ? `Index '${indexName}' exists` : `Index '${indexName}' does not exist`)

  if (indexExists) {
    logger.info(`Deleting index '${indexName}'...`)
    await opensearchClient.indices.delete({
      index: indexName,
    })
  }

  logger.info(`Creating index '${indexName}'...`)
  return opensearchClient.indices.create({
    index: indexName,
  })
}

const updateOSSProduct = ({
  product,
  indexName,
}: {
  product: ProductV2Models.IProductContract
  indexName: OpensearchIndex
}) => {
  const recordPromise: Promise<string> = new Promise(async (resolve, reject) => {
    try {
      await opensearchClient.update({
        id: product.id,
        index: indexName,
        body: { doc: product, doc_as_upsert: true },
      })
      logger.info(`${product.id} updated`)
      resolve(`${product.id} updated`)
    } catch (err) {
      metrics.addMetric(EMetricNames.INGESTION_FATAL_ERROR, MetricUnit.Count, 1)
      metrics.publishStoredMetrics()
      logger.error(JSON.stringify(err))
      reject(err)
    }
  })
  return recordPromise
}

const handler = async (event: IProductRawEvent) => {
  logger.info('Ingested event', { event })
  const indexName = OpensearchIndex.COMPANY_PRODUCTS

  logger.info(`Received event: ${JSON.parse(event.Records[0].body)}`)
  if (event.initialSetup) {
    return initialSetup(indexName)
  }

  const recordPromises: Promise<string>[] = []

  for (const record of event.Records) {
    logger.info('Ingested record', { record })
    const receivedEvent: IProductParsedEvent = JSON.parse(record.body)
    const { detail } = receivedEvent
    const { product } = detail

    const recordPromise = updateOSSProduct({
      product,
      indexName,
    })
    recordPromises.push(recordPromise)
  }

  return Promise.allSettled(recordPromises)
}

export const replayHandler = middy(handler).use(sqsPartialBatchFailureMiddleware())
