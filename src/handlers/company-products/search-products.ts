import { MetricUnit } from '@aws-lambda-powertools/metrics'
import { APIGatewayProxyEvent } from 'aws-lambda'
import { OpenSearchServerlessClient } from '../../common/aws-sdk-clients/opensearch'
import { middyWrapper } from '../../common/middy/middy'
import { logger } from '../../common/powertools/logger'
import { EMetricNames, metrics } from '../../common/powertools/metrics'
import { ResponseWriter } from '../../common/web/ResponseWriter'
import { OpensearchIndex } from '../../models/common/enums'
import { parseSearchProductsEnv } from '../../models/envs/search-products'
import { safeParseSearchProductsEvent } from '../../models/events/lambdas/search-products'
import {
  buildProductListQueryCore,
  buildProductSearchQueryCore,
  decodeCursor,
  encodeCursor,
  withCursorPagination,
} from '../../utils/opensearch-query'

export const handler = middyWrapper<APIGatewayProxyEvent>(async (event) => {
  try {
    logger.debug('Ingested event', { event })

    const env = parseSearchProductsEnv(process.env)
    const { data: parsedEvent, success, error } = safeParseSearchProductsEvent(event)

    if (!success || !parsedEvent) {
      return ResponseWriter.objectResponse(400, {
        detail: 'Invalid request format',
        error: error.format(),
        status: 400,
      })
    }

    logger.info('Parsed incoming event', { parsedEvent })

    const { queryTerm, limit, nextPageCursor, prevPageCursor } = parsedEvent.queryStringParameters

    const companyId = parsedEvent.pathParameters.companyId

    let body: Record<string, any>
    const isSearch = Boolean(queryTerm && queryTerm.length > 0)
    const isPrev = Boolean(prevPageCursor)

    if (isPrev) {
      const afterValues = decodeCursor(prevPageCursor!)
      if (isSearch) {
        const base = buildProductSearchQueryCore(queryTerm!, { companyId })
        body = {
          ...base,
          sort: [
            { _score: 'asc' },
            { updateDate: { order: 'asc', missing: '_first' } },
            { _id: 'desc' },
          ],
          size: limit + 1,
          track_total_hits: false,
          search_after: afterValues,
        }
      } else {
        const base = buildProductListQueryCore({ companyId })
        body = {
          ...base,
          sort: [
            { updateDate: { order: 'asc', missing: '_first' } },
            { 'name.keyword': 'desc' },
            { _id: 'desc' },
          ],
          size: limit + 1,
          track_total_hits: false,
          search_after: afterValues,
        }
      }
    } else {
      const base = isSearch
        ? buildProductSearchQueryCore(queryTerm!, { companyId })
        : buildProductListQueryCore({ companyId })
      body = withCursorPagination(base, { limit, after: nextPageCursor, trackTotalHits: false })
    }

    logger.debug('Built OS body', { body })

    const { client: osClient } = new OpenSearchServerlessClient({ endpoint: env.AOSS_ENDPOINT })
    const response = await osClient.search({
      index: OpensearchIndex.COMPANY_PRODUCTS,
      body,
    })

    let hits = response.body.hits?.hits ?? []
    const hasMorePrev = isPrev && hits.length > limit
    if (isPrev) {
      hits = hits.reverse()
      if (hasMorePrev) {
        hits = hits.slice(1)
      }
    }

    const firstSort = hits.length ? hits[0].sort : undefined
    const lastSort = hits.length ? hits[hits.length - 1].sort : undefined

    const nextPageCursorOutput =
      hits.length === limit && Array.isArray(lastSort) ? encodeCursor(lastSort) : undefined

    const prevPageCursorOutput =
      ((isPrev && hasMorePrev) || (!isPrev && Boolean(nextPageCursor))) && Array.isArray(firstSort)
        ? encodeCursor(firstSort)
        : undefined

    const products: object[] = hits.map((hit) => ({
      score: hit._score,
      data: hit._source,
    }))

    return ResponseWriter.objectResponse(200, {
      data: products,
      limit,
      count: products.length,
      nextPageCursor: nextPageCursorOutput,
      prevPageCursor: prevPageCursorOutput,
    })
  } catch (error: any) {
    metrics.addMetric(EMetricNames.QUERY_FATAL_ERROR, MetricUnit.Count, 1)
    metrics.publishStoredMetrics()
    logger.error('Error captured', {
      name: error?.name,
      message: error?.message,
      stack: error?.stack,
      raw: JSON.stringify(error, Object.getOwnPropertyNames(error)),
    })

    return ResponseWriter.objectResponse(400, {
      detail: 'Error ocurred on search',
      status: 400,
    })
  }
})
