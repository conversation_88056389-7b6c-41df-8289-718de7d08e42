import { OpensearchIndex } from '../models/common/enums'

export function encodeCursor(sortValues: unknown[]): string {
  return Buffer.from(JSON.stringify(sortValues)).toString('base64url')
}

export function decodeCursor(cursor: string): unknown[] {
  let arr: unknown
  try {
    const json = Buffer.from(cursor, 'base64url').toString('utf8')
    arr = JSON.parse(json)
  } catch {
    throw new Error('Invalid cursor: cannot decode')
  }
  if (!Array.isArray(arr)) {
    throw new Error('Invalid cursor: not an array')
  }
  return arr
}

export interface ProductSearchCoreOptions {
  companyId?: number | string
  isActive?: boolean
  isDeleted?: boolean
}

export function buildProductSearchQueryCore(
  queryTerm: string,
  { companyId, isActive = true, isDeleted = false }: ProductSearchCoreOptions = {},
) {
  const filter: any[] = [{ term: { isDeleted } }, { term: { isActive } }]

  if (companyId !== undefined && companyId !== null) {
    filter.unshift({ term: { companyId } })
  }

  return {
    query: {
      bool: {
        filter,
        should: [
          // Deterministic exact matches
          { term: { 'barcode.keyword': { value: queryTerm, boost: 300 } } },
          { term: { 'sku.keyword': { value: queryTerm, boost: 260 } } },

          // Deterministic prefix (type-ahead on keyword)
          { prefix: { 'barcode.keyword': { value: queryTerm, boost: 180 } } },
          { prefix: { 'sku.keyword': { value: queryTerm, boost: 160 } } },

          // Fuzzy multi-field text search
          {
            multi_match: {
              query: queryTerm,
              type: 'best_fields',
              fields: [
                'name^6',
                'shortName^4',
                'categoryName^3',
                'parentCategoryName^2',
                'supplierName^2',
                'description',
                'tags',
              ],
              fuzziness: 'AUTO',
              prefix_length: 2,
              operator: 'and',
              lenient: true,
              auto_generate_synonyms_phrase_query: false,
            },
          },

          // Phrase/Prefix boosts for very close matches
          { match_phrase: { name: { query: queryTerm, boost: 20 } } },
          { match_phrase_prefix: { name: { query: queryTerm, max_expansions: 20, boost: 10 } } },
        ],
        minimum_should_match: 1,
      },
    },
    highlight: {
      fields: { name: {}, categoryName: {}, description: {} },
      pre_tags: ['<em>'],
      post_tags: ['</em>'],
    },
    sort: [{ _score: 'desc' }, { updateDate: { order: 'desc', missing: '_last' } }, { _id: 'asc' }],
  }
}

interface ProductListCoreOptions extends ProductSearchCoreOptions {}

export function buildProductListQueryCore({
  companyId,
  isActive = true,
  isDeleted = false,
}: ProductListCoreOptions = {}) {
  const filter: any[] = [{ term: { isDeleted } }, { term: { isActive } }]
  if (companyId !== undefined && companyId !== null) filter.unshift({ term: { companyId } })

  return {
    query: { bool: { filter } },
    sort: [
      { updateDate: { order: 'desc', missing: '_last' } },
      { 'name.keyword': 'asc' },
      { _id: 'asc' },
    ],
  }
}

export interface CursorOptions {
  limit: number
  after?: string
  trackTotalHits?: boolean
}

export function withCursorPagination<T extends Record<string, any>>(
  coreBody: T,
  { limit, after, trackTotalHits = false }: CursorOptions,
) {
  if (!Number.isInteger(limit) || limit <= 0) {
    throw new Error('limit must be an integer > 0')
  }

  const extra: Record<string, any> = { size: limit, track_total_hits: trackTotalHits }

  if (after !== undefined) {
    const sortValues = decodeCursor(after)
    extra.search_after = sortValues
  }

  return { ...coreBody, ...extra }
}

export interface BuildProductSearchRequestOptions extends ProductSearchCoreOptions, CursorOptions {
  index: OpensearchIndex
}

export function buildProductSearchRequest(
  q: string,
  { index, ...opts }: BuildProductSearchRequestOptions,
) {
  const core = buildProductSearchQueryCore(q, opts)
  const body = withCursorPagination(core, opts)
  return { index, body }
}
