import dayjs from 'dayjs'
import { BlazeCanadaAPI } from '../apis/BlazeCanadaAPI'
import { DomainAdapter } from '../models/adapters/DomainAdapter'
import {
  EProductSellType,
  EProductStrainType,
  TCreateBlazeCanadaProductCSVRow,
  TCreateBlazeCanadaProductDetailsOverrideCSVRow,
  TCreateBlazeCanadaProductPriceOverrideCSVRow,
} from '../models/apis/blaze-canada/Product'
import { EDomain } from '../models/common/enums'
import { TAOProductContract } from '../models/contracts/auto-onboarding/Product/ProductContract'
import {
  ProductExportRowFields,
  TProductExportRow,
  TDedupedProductExportRow,
  parseProductExport,
} from '../models/contracts/auto-onboarding/Product/ProductExportRow'
import { AOProductService } from '../services/auto-onboarding/AOProductService'
import { groupedRecordsByKey, hashCanonicalStr, unifyRecordGroup } from './utils'
import { canonicalize, toBooleanString } from '../utils/string'
import { uploadCsvToS3 } from '../utils/csv'
import { TStage } from '../utils/stage'

export class ProductAdapter
  implements DomainAdapter<TProductExportRow, TAOProductContract, never, true>
{
  private companyId: string
  private service: AOProductService

  constructor(companyId: string) {
    this.companyId = companyId
    this.service = new AOProductService(companyId)
  }

  private getIdFromExportRow(record: TProductExportRow): string {
    return record.id
  }

  private convertSaleTypeToBlazeCanada(
    contractSaleType: TAOProductContract['saleType'],
  ): TCreateBlazeCanadaProductCSVRow['Sell Type'] {
    const mapping: Record<
      NonNullable<TAOProductContract['saleType']>,
      TCreateBlazeCanadaProductCSVRow['Sell Type']
    > = {
      Recreational: EProductSellType.RECREATIONAL,
      Medicinal: EProductSellType.MEDICINAL,
      Both: EProductSellType.BOTH,
    }

    return contractSaleType ? mapping[contractSaleType] : undefined
  }

  private convertFlowerTypeToBlazeCanada(
    contractFlowerType: TAOProductContract['flowerType'],
  ): TCreateBlazeCanadaProductCSVRow['Flower Type'] {
    if (!contractFlowerType) return undefined
    const canonicalFlowerType = canonicalize(contractFlowerType)

    if (canonicalFlowerType.includes('sativadominant')) return EProductStrainType.SATIVA_DOMINANT
    if (canonicalFlowerType.includes('indica')) return EProductStrainType.INDICA_DOMINANT

    if (canonicalFlowerType.includes('sativa')) return EProductStrainType.SATIVA
    if (canonicalFlowerType.includes('indica')) return EProductStrainType.INDICA

    if (canonicalFlowerType.includes('blend')) return EProductStrainType.BLEND
    if (canonicalFlowerType.includes('balanced')) return EProductStrainType.BALANCED
    if (canonicalFlowerType.includes('hybrid')) return EProductStrainType.HYBRID

    if (canonicalFlowerType.includes('cbd')) return EProductStrainType.CBD

    return undefined
  }

  public isBulkDomain(): true {
    return true
  }

  public parse = parseProductExport

  public getIdFromContract(contract: TAOProductContract): string {
    return contract.id
  }

  public getDedupeKeyFromContract(contract: TAOProductContract): string {
    return contract.key
  }

  public csvKey() {
    return `companies/${this.companyId}/products/company-${this.companyId}-products-export.csv`
  }

  public dedupeKey(record: TProductExportRow): string {
    return hashCanonicalStr(record.name)
  }

  public unifyMatchingRecords(records: TProductExportRow[]): TDedupedProductExportRow[] {
    const recordGroupsMap = groupedRecordsByKey<TProductExportRow>(records, this.dedupeKey)
    const unifiedRecords: TDedupedProductExportRow[] = []
    const recordGroups = recordGroupsMap.entries()

    for (const [key, group] of recordGroups) {
      const unifiedRecord = unifyRecordGroup(key, group, {
        getId: this.getIdFromExportRow,
        fields: ProductExportRowFields,
      })
      unifiedRecords.push(unifiedRecord)
    }

    return unifiedRecords
  }

  public async saveRecords(records: TDedupedProductExportRow[]): Promise<number[]> {
    const contracts: TAOProductContract[] = records.map((record) => {
      const {
        active,
        customGramType,
        customWeight,
        enableWeedMap,
        imageUrl1,
        imageUrl2,
        imageUrl3,
        secondaryVendorName1,
        secondaryVendorName2,
        secondaryVendorName3,
        secondaryVendorName4,
        secondaryVendorName5,
        secondaryVendorName6,
        secondaryVendorName7,
        secondaryVendorName8,
        secondaryVendorName9,
        secondaryVendorName10,
        SKU,
        pricingTemplateName,
        productSaleType,
        tags: srcTags,
        overrides: srcOverrides,
        retailPrice: srcRetailPrice,
        wholesaleCost: srcWholesaleCost,
        ...commonKeys
      } = record
      return {
        ...commonKeys,
        isActive: active,
        gramType: customGramType,
        weight: customWeight,
        pricingTemplate: pricingTemplateName,
        tags: srcTags?.split(','),
        isWeedmapsEnabled: enableWeedMap,
        sku: SKU,
        saleType: productSaleType,
        retailPrice: srcRetailPrice ? srcRetailPrice * 100 : undefined,
        wholesaleCost: srcWholesaleCost ? srcWholesaleCost * 100 : undefined,
        imageUrls: [imageUrl1, imageUrl2, imageUrl3].filter(
          (imageUrl) => typeof imageUrl === 'string',
        ) as string[],
        secondaryVendorNames: [
          secondaryVendorName1,
          secondaryVendorName2,
          secondaryVendorName3,
          secondaryVendorName4,
          secondaryVendorName5,
          secondaryVendorName6,
          secondaryVendorName7,
          secondaryVendorName8,
          secondaryVendorName9,
          secondaryVendorName10,
        ].filter((vendorName) => typeof vendorName === 'string') as string[],
        overrides: srcOverrides.map((override) => {
          const { tags: srcOverrideTags, ...commonOverrideKeys } = override
          return {
            ...commonOverrideKeys,
            tags: srcOverrideTags?.split(','),
          }
        }),
        domain: EDomain.PRODUCT,
        createdAt: dayjs(),
        updatedAt: dayjs(),
      }
    })
    return this.service.bulkCreate(contracts)
  }

  public async getSavedRecordsByBatch(batchNumber: number): Promise<TAOProductContract[]> {
    return this.service.getByBatch(batchNumber)
  }

  public async bulkCreateRecordsInBlazeCanada(
    records: TAOProductContract[],
    {
      api,
      bucketName,
      stage,
      batchNumber,
    }: {
      api: BlazeCanadaAPI
      bucketName: string
      stage: TStage
      batchNumber: number
    },
  ) {
    const blazeCanadaBaseContracts: TCreateBlazeCanadaProductCSVRow[] = []
    const blazeCanadaDetailsOverrides: TCreateBlazeCanadaProductDetailsOverrideCSVRow[] = []
    const blazeCanadaPriceOverrides: TCreateBlazeCanadaProductPriceOverrideCSVRow[] = []

    for (const record of records) {
      const baseContract: TCreateBlazeCanadaProductCSVRow = {
        ID: '', // Blaze Canada expects an empty string for the ID while keeping the column
        Name: record.name,
        'Is Active': toBooleanString(record.isActive),
        'Cannabis Weight': record.weight?.toFixed(2).toString(), // TODO: Confirm if weight is cannabis weight and if it is in grams
        'Weight Unit': record.gramType === 'GRAM' ? 'gram' : undefined,
        Category: record.categoryName,
        Brand: record.brandName,
        //TODO: add pricing template here later.
        'Pricing Template': '',
        Vendor: record.vendorName,
        'Image URL': record.imageUrls?.join(','),
        Tags: record.tags?.join(','),
        'Sell Type': this.convertSaleTypeToBlazeCanada(record.saleType),
        Created: record.createdAt.toISOString(),
        Updated: record.updatedAt.toISOString(),
        Description: record.description,
        'Retail Price': record.retailPrice ? `$${record.retailPrice.toFixed(2)}` : undefined,
        SKU: record.sku,
        'Flower Type': this.convertFlowerTypeToBlazeCanada(record.flowerType),
        'Wholesale Cost': record.wholesaleCost ? `$${record.wholesaleCost.toFixed(2)}` : undefined,
        'Available Online':
          record.isAvailableOnline !== undefined ? toBooleanString(record.isAvailableOnline) : '',
      }
      blazeCanadaBaseContracts.push(baseContract)

      for (const override of record.overrides) {
        const detailsOverrideContract: TCreateBlazeCanadaProductDetailsOverrideCSVRow = {
          ID: record.id,
          SKU: override.sku ?? '',
          'Is Active': toBooleanString(override.isActive ?? false),
          'Product Name': record.name,
          Shop: override.shopId,
          Price: override.retailPrice ? `$${override.retailPrice.toFixed(2)}` : '',
          'Wholesale Cost': override.wholesaleCost ? `$${override.wholesaleCost.toFixed(2)}` : '',
        }
        blazeCanadaDetailsOverrides.push(detailsOverrideContract)

        const priceOverrideContract: TCreateBlazeCanadaProductPriceOverrideCSVRow = {
          ID: record.id,
          'Product Name': record.name,
          Shop: override.shopId,
          Price: override.retailPrice ? `$${override.retailPrice.toFixed(2)}` : '',
        }
        blazeCanadaPriceOverrides.push(priceOverrideContract)
      }
    }

    const { key: baseContractsFileKey } = await uploadCsvToS3(blazeCanadaBaseContracts, {
      bucket: bucketName,
      key: `auto-onboarding/companies/${this.companyId}/products/${stage}-company-${this.companyId}-domain-products-batch-${batchNumber}.csv`,
    })

    await api.bulkCreateProductsFromCSV(this.companyId, {
      bucketName,
      bucketRegion: 'us-east-1',
      enableCreate: true,
      fileName: baseContractsFileKey,
    })

    const { key: detailsOverridesFileKey } = await uploadCsvToS3(blazeCanadaBaseContracts, {
      bucket: bucketName,
      key: `auto-onboarding/companies/${this.companyId}/products/overrides/details/${stage}-company-${this.companyId}-domain-products-details-overrides-batch-${batchNumber}.csv`,
    })

    await api.bulkCreateProductsDetailsFromCSV(this.companyId, {
      bucketName,
      bucketRegion: 'us-east-1',
      fileName: detailsOverridesFileKey,
    })

    const { key: pricesOverridesFileKey } = await uploadCsvToS3(blazeCanadaBaseContracts, {
      bucket: bucketName,
      key: `auto-onboarding/companies/${this.companyId}/products/overrides/prices/${stage}-company-${this.companyId}-domain-products-prices-overrides-batch-${batchNumber}.csv`,
    })

    await api.bulkCreateProductsPricesFromCSV(this.companyId, {
      bucketName,
      bucketRegion: 'us-east-1',
      fileName: pricesOverridesFileKey,
    })
  }
}
