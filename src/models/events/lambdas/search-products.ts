import { z } from 'zod'

const SearchProductsEventSchema = z.object({
  pathParameters: z.object({
    companyId: z
      .string()
      .min(1)
      .regex(/^[0-9]*$/),
  }),
  queryStringParameters: z
    .object({
      queryTerm: z.string().trim().min(1).optional(),
      prevPageCursor: z.string().trim().min(1).optional(),
      nextPageCursor: z.string().trim().min(1).optional(),
      limit: z.coerce.number().int().min(1).max(100),
    })
    .strict()
    .refine(
      (values) => {
        if (values.prevPageCursor) return !values.nextPageCursor
        if (values.nextPageCursor) return !values.prevPageCursor
        return true
      },
      { message: 'Specify either prevPageCursor or nextPageCursor' },
    ),
})

export type TSearchProductsEvent = z.infer<typeof SearchProductsEventSchema>

export const safeParseSearchProductsEvent = (data: unknown) => {
  return SearchProductsEventSchema.safeParse(data)
}
