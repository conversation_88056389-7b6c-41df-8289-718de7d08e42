import { z } from 'zod'
import { TDedupedRecord } from '../Overrides'
import { MongoObjectId } from '../../../common/zod'

const EWeightPerUnit = z.enum([
  'EACH',
  'HALF_GRAM',
  'FULL_GRAM',
  'TWO_GRAMS',
  'EIGHTH',
  'FOURTH',
  'HALF_OUNCE',
  'OUNCE',
  'CUSTOM_GRAMS',
  'POUND',
  'FLUID_OZ',
])
const ECustomGramType = z.enum(['GRAM', 'MILLIGRAM', 'MILLILITER', 'OUNCE', 'FLUID_OUNCE'])
const EProductSaleType = z.enum(['Medicinal', 'Recreational', 'Both'])

const ImageUrl = z.string().url().optional()
const SecondaryVendorName = z.string().min(1).optional()

const ProductExportRowSchema = z.object({
  id: MongoObjectId,
  companyId: MongoObjectId,
  shopId: MongoObjectId,
  masterId: MongoObjectId.optional(),
  name: z.string().min(1),
  active: z.coerce.boolean(),
  categoryName: z.string().min(1).optional(),
  brandName: z.string().min(1).optional(),
  pricingTemplateName: z.string().min(1).optional(),
  weightPerUnit: EWeightPerUnit,
  customGramType: ECustomGramType,
  customWeight: z.coerce.number().optional(),
  description: z.string().min(1).optional(),
  enableWeedMap: z.coerce.boolean(),
  flowerType: z.string().min(1).optional(),
  imageUrl1: ImageUrl,
  imageUrl2: ImageUrl,
  imageUrl3: ImageUrl,
  masterCategoryId: MongoObjectId.optional(),
  productSaleType: EProductSaleType.optional(),
  retailPrice: z.coerce.number().optional(),
  SKU: z.string().min(1).optional(),
  tags: z.string().min(1).optional(),
  wholesaleCost: z.coerce.number().optional(),
  isAvailableOnline: z.coerce.boolean(),
  vendorName: z.string().min(1).optional(),
  secondaryVendorName1: SecondaryVendorName,
  secondaryVendorName2: SecondaryVendorName,
  secondaryVendorName3: SecondaryVendorName,
  secondaryVendorName4: SecondaryVendorName,
  secondaryVendorName5: SecondaryVendorName,
  secondaryVendorName6: SecondaryVendorName,
  secondaryVendorName7: SecondaryVendorName,
  secondaryVendorName8: SecondaryVendorName,
  secondaryVendorName9: SecondaryVendorName,
  secondaryVendorName10: SecondaryVendorName,
})

const ProductExportSchema = ProductExportRowSchema.array().nonempty()

export type TProductExportRow = z.infer<typeof ProductExportRowSchema>
export type TDedupedProductExportRow = TDedupedRecord<TProductExportRow, 'id', 'shopId'>
export type TProductExport = z.infer<typeof ProductExportSchema>
export const ProductExportRowFields = Object.keys(
  ProductExportRowSchema.shape,
) as (keyof TProductExportRow)[]

export const parseProductExportRow = (data: unknown) => {
  return ProductExportRowSchema.parse(data)
}

export const parseProductExport = (data: unknown): TProductExport => {
  return ProductExportSchema.parse(data)
}
