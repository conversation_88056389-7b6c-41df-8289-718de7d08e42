import { OpensearchIndex } from '../../../models/common/enums'
import {
  buildProductListQueryCore,
  buildProductSearchQueryCore,
  buildProductSearchRequest,
  decodeCursor,
  encodeCursor,
  withCursorPagination,
} from '../../../utils/opensearch-query'

describe('cursor helpers', () => {
  it('encodes and decodes sort values round-trip (relevance-first: [score, updateDate, _id])', () => {
    const values: [number, string, string] = [
      0.987654,
      '2025-08-05T21:34:23.000Z',
      '7fef9e9f-ed39-47c3',
    ]
    const cur = encodeCursor(values)
    expect(typeof cur).toBe('string')
    const back = decodeCursor(cur)
    expect(back).toEqual(values)
  })

  it('encodes and decodes sort values round-trip (list: [updateDate, name, _id])', () => {
    const values = ['2025-08-05T21:34:23.000Z', 'Alpha', '7fef9e9f-ed39-47c3']
    const cur = encodeCursor(values)
    expect(typeof cur).toBe('string')
    const back = decodeCursor(cur)
    expect(back).toEqual(values)
  })

  it('throws on invalid cursor', () => {
    expect(() => decodeCursor('not-base64')).toThrow(/Invalid cursor/)
    const notArray = Buffer.from('{"a":1}', 'utf8').toString('base64url')
    expect(() => decodeCursor(notArray)).toThrow(/not an array/)
  })
})

describe('buildProductSearchQueryCore', () => {
  it('builds core body for q="testing" (no pagination fields)', () => {
    const q = 'testing'
    const core = buildProductSearchQueryCore(q)

    expect((core as any).size).toBeUndefined()
    expect((core as any).search_after).toBeUndefined()
    expect((core as any).track_total_hits).toBeUndefined()

    expect((core as any).query.bool.filter).toEqual([
      { term: { isDeleted: false } },
      { term: { isActive: true } },
    ])

    const shouldClauses = (core as any).query.bool.should
    const termBarcode = shouldClauses.find((c: any) => 'term' in c && c.term['barcode.keyword'])
    const termSku = shouldClauses.find((c: any) => 'term' in c && c.term['sku.keyword'])
    const prefixBarcode = shouldClauses.find(
      (c: any) => 'prefix' in c && c.prefix['barcode.keyword'],
    )
    const prefixSku = shouldClauses.find((c: any) => 'prefix' in c && c.prefix['sku.keyword'])

    expect(termBarcode).toEqual({ term: { 'barcode.keyword': { value: q, boost: 300 } } })
    expect(termSku).toEqual({ term: { 'sku.keyword': { value: q, boost: 260 } } })
    expect(prefixBarcode).toEqual({ prefix: { 'barcode.keyword': { value: q, boost: 180 } } })
    expect(prefixSku).toEqual({ prefix: { 'sku.keyword': { value: q, boost: 160 } } })

    const multiMatch = shouldClauses.find((c: any) => 'multi_match' in c)?.multi_match
    expect(multiMatch).toMatchObject({
      query: q,
      type: 'best_fields',
      fuzziness: 'AUTO',
      prefix_length: 2,
      operator: 'and',
      lenient: true,
      auto_generate_synonyms_phrase_query: false,
    })
    expect(multiMatch.fields).toEqual([
      'name^6',
      'shortName^4',
      'categoryName^3',
      'parentCategoryName^2',
      'supplierName^2',
      'description',
      'tags',
    ])

    expect((core as any).sort).toEqual([
      { _score: 'desc' },
      { updateDate: { order: 'desc', missing: '_last' } },
      { _id: 'asc' },
    ])
  })

  it('injects companyId when provided', () => {
    const core = buildProductSearchQueryCore('testing', { companyId: 13 })
    const filters = (core as any).query.bool.filter
    expect(filters[0]).toEqual({ term: { companyId: 13 } })
    expect(filters).toEqual(
      expect.arrayContaining([{ term: { isDeleted: false } }, { term: { isActive: true } }]),
    )
  })
})

describe('buildProductListQueryCore', () => {
  it('builds listing core with stable sort (no pagination fields)', () => {
    const core = buildProductListQueryCore({ companyId: 13 })

    expect((core as any).size).toBeUndefined()
    expect((core as any).search_after).toBeUndefined()
    expect((core as any).track_total_hits).toBeUndefined()

    expect((core as any).query.bool.filter).toEqual([
      { term: { companyId: 13 } },
      { term: { isDeleted: false } },
      { term: { isActive: true } },
    ])

    expect((core as any).sort).toEqual([
      { updateDate: { order: 'desc', missing: '_last' } },
      { 'name.keyword': 'asc' },
      { _id: 'asc' },
    ])
  })
})

describe('withCursorPagination', () => {
  it('adds size/track_total_hits and search_after when cursor provided (relevance-first shape), without mutating core', () => {
    const core = buildProductSearchQueryCore('testing')
    const original = JSON.parse(JSON.stringify(core))

    const values: [number, string, string] = [0.5, '2025-08-05T21:34:23.000Z', '7fef9e9f-ed39-47c3']
    const cursor = encodeCursor(values)

    const out = withCursorPagination(core, { limit: 25, after: cursor, trackTotalHits: false })

    expect(out).not.toBe(core)
    expect(core).toEqual(original)

    expect(out.size).toBe(25)
    expect(out.track_total_hits).toBe(false)
    expect(out.search_after).toEqual(values)
  })

  it('defaults to track_total_hits=false, without search_after on first page', () => {
    const out = withCursorPagination(buildProductSearchQueryCore('testing'), { limit: 20 })
    expect(out.size).toBe(20)
    expect(out.track_total_hits).toBe(false)
    expect((out as any).search_after).toBeUndefined()
  })

  it('validates limit', () => {
    const core = buildProductSearchQueryCore('testing')
    expect(() => withCursorPagination(core, { limit: 0 })).toThrow(/limit.*> 0/)
    expect(() => withCursorPagination(core, { limit: 10.5 as any })).toThrow(/integer/)
  })
})

describe('buildProductSearchRequest with cursor', () => {
  it('returns { index, body } and keeps sort inside body; adds search_after when cursor is present', () => {
    const cursor = encodeCursor([0.9, '2025-08-05T21:34:23.000Z', '7fef9e9f-ed39-47c3'])
    const req = buildProductSearchRequest('testing', {
      index: OpensearchIndex.COMPANY_PRODUCTS,
      limit: 10,
      after: cursor,
      trackTotalHits: false,
    })

    expect(req.index).toBe(OpensearchIndex.COMPANY_PRODUCTS)
    expect((req as any).sort).toBeUndefined()
    expect(req.body).toHaveProperty('sort')
    expect(req.body.size).toBe(10)
    expect(req.body.track_total_hits).toBe(false)
    expect(req.body.search_after).toEqual([0.9, '2025-08-05T21:34:23.000Z', '7fef9e9f-ed39-47c3'])
  })

  it('omits search_after when no cursor is provided (first page)', () => {
    const req = buildProductSearchRequest('testing', {
      index: OpensearchIndex.COMPANY_PRODUCTS,
      limit: 10,
    })
    expect(req.body.size).toBe(10)
    expect(req.body.search_after).toBeUndefined()
  })
})
