import { safeParseSearchProductsEvent } from '../../../../models/events/lambdas/search-products'

const baseEvent = {
  pathParameters: { companyId: '13' },
  queryStringParameters: { limit: 10 } as any,
}

describe('SearchProductsEvent schema (mutually exclusive cursors)', () => {
  it('accepts no cursors (first page)', () => {
    const res = safeParseSearchProductsEvent(baseEvent)
    expect(res.success).toBe(true)
  })

  it('accepts only nextPageCursor', () => {
    const event = {
      ...baseEvent,
      queryStringParameters: { ...baseEvent.queryStringParameters, nextPageCursor: 'abc' },
    }
    const res = safeParseSearchProductsEvent(event)
    expect(res.success).toBe(true)
  })

  it('accepts only prevPageCursor', () => {
    const event = {
      ...baseEvent,
      queryStringParameters: { ...baseEvent.queryStringParameters, prevPageCursor: 'xyz' },
    }
    const res = safeParseSearchProductsEvent(event)
    expect(res.success).toBe(true)
  })

  it('rejects both cursors at once', () => {
    const event = {
      ...baseEvent,
      queryStringParameters: {
        ...baseEvent.queryStringParameters,
        nextPageCursor: 'abc',
        prevPageCursor: 'xyz',
      },
    }
    const res = safeParseSearchProductsEvent(event)
    expect(res.success).toBe(false)
    // message from `refine`
    const msg = (res as any).error?.issues?.map((i: any) => i.message).join(' | ')
    expect(msg).toMatch(/Specify either prevPageCursor or nextPageCursor/)
  })

  it('allows listing without queryTerm', () => {
    const res = safeParseSearchProductsEvent({
      ...baseEvent,
      queryStringParameters: { limit: 5 },
    })
    expect(res.success).toBe(true)
  })

  it('allows search when queryTerm is provided', () => {
    const res = safeParseSearchProductsEvent({
      ...baseEvent,
      queryStringParameters: { limit: 5, queryTerm: 'testing' },
    })
    expect(res.success).toBe(true)
  })

  it('rejects extraneous values', () => {
    const res = safeParseSearchProductsEvent({
      ...baseEvent,
      queryStringParameters: { limit: 5, queryTerm: 'testing', unkownKey: 'unknown-value' },
    })
    expect(res.success).toBe(false)
  })
})
