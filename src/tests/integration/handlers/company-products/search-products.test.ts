import type { APIGatewayProxyEvent } from 'aws-lambda'
import { handler } from '../../../../handlers/company-products/search-products'
import { encodeCursor } from '../../../../utils/opensearch-query'

jest.mock('../../../../models/envs/search-products', () => ({
  parseSearchProductsEnv: () => ({ AOSS_ENDPOINT: 'https://example' }),
}))

jest.mock('../../../../common/middy/middy', () => ({
  middyWrapper: (fn: any) => fn,
}))

jest.mock('../../../../common/aws-sdk-clients/opensearch', () => {
  const search = jest.fn()
  return {
    __esModule: true,
    OpenSearchServerlessClient: jest.fn().mockImplementation(() => ({
      client: { search },
    })),
    __searchMock: search,
  }
})
const { __searchMock: searchMock } = jest.requireMock(
  '../../../../common/aws-sdk-clients/opensearch',
) as { __searchMock: jest.Mock }

beforeEach(() => {
  jest.clearAllMocks()
  searchMock.mockReset()
})

function makeEvent(
  params: Partial<APIGatewayProxyEvent> & {
    qs: Record<string, any>
    companyId?: string
  },
): APIGatewayProxyEvent {
  const { qs, companyId = '13', ...rest } = params
  return {
    body: null,
    headers: {},
    multiValueHeaders: {},
    httpMethod: 'GET',
    isBase64Encoded: false,
    path: `/companies/${companyId}/products/search`,
    pathParameters: { companyId },
    queryStringParameters: {
      limit: qs.limit != null ? String(qs.limit) : undefined,
      queryTerm: qs.queryTerm,
      nextPageCursor: qs.nextPageCursor,
      prevPageCursor: qs.prevPageCursor,
    } as any,
    multiValueQueryStringParameters: null,
    stageVariables: null,
    resource: '',
    requestContext: {} as any,
    ...rest,
  }
}

describe('handler - search', () => {
  it('hits === limit, not first and not last -> both cursors', async () => {
    const limit = 2
    const hits = [
      {
        _id: 'A',
        _score: 9,
        _source: { id: 'A' },
        sort: [9, '2025-08-12T00:00:00.000Z', 'A'] as [number, string, string],
      },
      {
        _id: 'B',
        _score: 8,
        _source: { id: 'B' },
        sort: [8, '2025-08-11T00:00:00.000Z', 'B'] as [number, string, string],
      },
    ]
    searchMock.mockResolvedValue({ body: { hits: { hits } } })
    const nextCursorIn = encodeCursor([10, '2025-08-13T00:00:00.000Z', 'Z'])
    const res: any = await handler(
      makeEvent({ qs: { queryTerm: 't', limit, nextPageCursor: nextCursorIn } }),
      undefined as any,
    )
    const body = JSON.parse(res.body)
    expect(body.nextPageCursor).toBeDefined()
    expect(body.prevPageCursor).toBeDefined()
  })

  it('hits === limit, first page -> only next cursor', async () => {
    const limit = 2
    const hits = [
      {
        _id: 'A',
        _score: 9,
        _source: { id: 'A' },
        sort: [9, '2025-08-12T00:00:00.000Z', 'A'] as [number, string, string],
      },
      {
        _id: 'B',
        _score: 8,
        _source: { id: 'B' },
        sort: [8, '2025-08-11T00:00:00.000Z', 'B'] as [number, string, string],
      },
    ]
    searchMock.mockResolvedValue({ body: { hits: { hits } } })
    const res: any = await handler(makeEvent({ qs: { queryTerm: 't', limit } }), undefined as any)
    const body = JSON.parse(res.body)
    expect(body.nextPageCursor).toBeDefined()
    expect(body.prevPageCursor).toBeUndefined()
  })

  it('last page (hits < limit), not first -> only prev cursor', async () => {
    const limit = 3
    const hits = [
      {
        _id: 'A',
        _score: 9,
        _source: { id: 'A' },
        sort: [9, '2025-08-12T00:00:00.000Z', 'A'] as [number, string, string],
      },
      {
        _id: 'B',
        _score: 8,
        _source: { id: 'B' },
        sort: [8, '2025-08-11T00:00:00.000Z', 'B'] as [number, string, string],
      },
    ]
    searchMock.mockResolvedValue({ body: { hits: { hits } } })
    const nextCursorIn = encodeCursor([10, '2025-08-13T00:00:00.000Z', 'Z'])
    const res: any = await handler(
      makeEvent({ qs: { queryTerm: 't', limit, nextPageCursor: nextCursorIn } }),
      undefined as any,
    )
    const body = JSON.parse(res.body)
    expect(body.nextPageCursor).toBeUndefined()
    expect(body.prevPageCursor).toBeDefined()
  })

  it('hits < limit and first page -> no cursors', async () => {
    const limit = 3
    const hits = [
      {
        _id: 'A',
        _score: 9,
        _source: { id: 'A' },
        sort: [9, '2025-08-12T00:00:00.000Z', 'A'] as [number, string, string],
      },
      {
        _id: 'B',
        _score: 8,
        _source: { id: 'B' },
        sort: [8, '2025-08-11T00:00:00.000Z', 'B'] as [number, string, string],
      },
    ]
    searchMock.mockResolvedValue({ body: { hits: { hits } } })
    const res: any = await handler(makeEvent({ qs: { queryTerm: 't', limit } }), undefined as any)
    const body = JSON.parse(res.body)
    expect(body.nextPageCursor).toBeUndefined()
    expect(body.prevPageCursor).toBeUndefined()
  })

  it('prev request landing on first page -> next cursor only (no prev)', async () => {
    const limit = 5
    const backwardHits = [
      {
        _id: 's1',
        _score: 5,
        _source: { id: 's1' },
        sort: [5, '2025-08-05T00:00:00.000Z', 's1'] as [number, string, string],
      },
      {
        _id: 's2',
        _score: 4,
        _source: { id: 's2' },
        sort: [4, '2025-08-04T00:00:00.000Z', 's2'] as [number, string, string],
      },
      {
        _id: 's3',
        _score: 3,
        _source: { id: 's3' },
        sort: [3, '2025-08-03T00:00:00.000Z', 's3'] as [number, string, string],
      },
      {
        _id: 's4',
        _score: 2,
        _source: { id: 's4' },
        sort: [2, '2025-08-02T00:00:00.000Z', 's4'] as [number, string, string],
      },
      {
        _id: 's5',
        _score: 1,
        _source: { id: 's5' },
        sort: [1, '2025-08-01T00:00:00.000Z', 's5'] as [number, string, string],
      },
    ]

    const { __searchMock: searchMock } = jest.requireMock(
      '../../../../common/aws-sdk-clients/opensearch',
    ) as { __searchMock: jest.Mock }
    searchMock.mockResolvedValue({ body: { hits: { hits: backwardHits } } })

    const prevCursorIn = encodeCursor([6, '2025-08-06T00:00:00.000Z', 's6'])
    const res: any = await handler(
      makeEvent({ qs: { queryTerm: 'testing', limit, prevPageCursor: prevCursorIn } }) as any,
      undefined as any,
    )
    const body = JSON.parse(res.body)
    expect(body.count).toBe(5)
    expect(body.nextPageCursor).toBeDefined()
    expect(body.prevPageCursor).toBeUndefined()
  })

  it('prev request, middle page -> both cursors', async () => {
    const limit = 5
    const backwardHits = [
      {
        _id: 's1',
        _score: 1,
        _source: { id: 's1' },
        sort: [1, '2025-08-01T00:00:00.000Z', 's1'] as [number, string, string],
      },
      {
        _id: 's2',
        _score: 2,
        _source: { id: 's2' },
        sort: [2, '2025-08-02T00:00:00.000Z', 's2'] as [number, string, string],
      },
      {
        _id: 's3',
        _score: 3,
        _source: { id: 's3' },
        sort: [3, '2025-08-03T00:00:00.000Z', 's3'] as [number, string, string],
      },
      {
        _id: 's4',
        _score: 4,
        _source: { id: 's4' },
        sort: [4, '2025-08-04T00:00:00.000Z', 's4'] as [number, string, string],
      },
      {
        _id: 's5',
        _score: 5,
        _source: { id: 's5' },
        sort: [5, '2025-08-05T00:00:00.000Z', 's5'] as [number, string, string],
      },
      {
        _id: 's6',
        _score: 6,
        _source: { id: 's6' },
        sort: [6, '2025-08-06T00:00:00.000Z', 's6'] as [number, string, string],
      },
    ]
    const { __searchMock: searchMock } = jest.requireMock(
      '../../../../common/aws-sdk-clients/opensearch',
    ) as { __searchMock: jest.Mock }
    searchMock.mockResolvedValue({ body: { hits: { hits: backwardHits } } })

    const prevCursorIn = encodeCursor([7, '2025-08-07T00:00:00.000Z', 's7'])
    const res: any = await handler(
      makeEvent({ qs: { queryTerm: 'testing', limit, prevPageCursor: prevCursorIn } }) as any,
      undefined as any,
    )
    const body = JSON.parse(res.body)
    expect(body.count).toBe(limit)
    expect(body.nextPageCursor).toBeDefined()
    expect(body.prevPageCursor).toBeDefined()
  })
})

describe('handler - list', () => {
  it('hits === limit, not first and not last -> both cursors', async () => {
    const limit = 2
    const hits = [
      {
        _id: '1',
        _score: null,
        _source: { id: '1' },
        sort: ['2025-08-12T00:00:00.000Z', 'Alpha', '1'] as [string, string, string],
      },
      {
        _id: '2',
        _score: null,
        _source: { id: '2' },
        sort: ['2025-08-11T00:00:00.000Z', 'Bravo', '2'] as [string, string, string],
      },
    ]
    searchMock.mockResolvedValue({ body: { hits: { hits } } })
    const nextCursorIn = encodeCursor(['2025-08-13T00:00:00.000Z', 'Zulu', 'Z'])
    const res: any = await handler(
      makeEvent({ qs: { limit, nextPageCursor: nextCursorIn } }),
      undefined as any,
    )
    const body = JSON.parse(res.body)
    expect(body.nextPageCursor).toBeDefined()
    expect(body.prevPageCursor).toBeDefined()
  })

  it('hits === limit, first page -> only next cursor', async () => {
    const limit = 2
    const hits = [
      {
        _id: '1',
        _score: null,
        _source: { id: '1' },
        sort: ['2025-08-12T00:00:00.000Z', 'Alpha', '1'] as [string, string, string],
      },
      {
        _id: '2',
        _score: null,
        _source: { id: '2' },
        sort: ['2025-08-11T00:00:00.000Z', 'Bravo', '2'] as [string, string, string],
      },
    ]
    searchMock.mockResolvedValue({ body: { hits: { hits } } })
    const res: any = await handler(makeEvent({ qs: { limit } }), undefined as any)
    const body = JSON.parse(res.body)
    expect(body.nextPageCursor).toBeDefined()
    expect(body.prevPageCursor).toBeUndefined()
  })

  it('last page (hits < limit), not first -> only prev cursor', async () => {
    const limit = 3
    const hits = [
      {
        _id: '1',
        _score: null,
        _source: { id: '1' },
        sort: ['2025-08-12T00:00:00.000Z', 'Alpha', '1'] as [string, string, string],
      },
      {
        _id: '2',
        _score: null,
        _source: { id: '2' },
        sort: ['2025-08-11T00:00:00.000Z', 'Bravo', '2'] as [string, string, string],
      },
    ]
    searchMock.mockResolvedValue({ body: { hits: { hits } } })
    const nextCursorIn = encodeCursor(['2025-08-13T00:00:00.000Z', 'Zulu', 'Z'])
    const res: any = await handler(
      makeEvent({ qs: { limit, nextPageCursor: nextCursorIn } }),
      undefined as any,
    )
    const body = JSON.parse(res.body)
    expect(body.nextPageCursor).toBeUndefined()
    expect(body.prevPageCursor).toBeDefined()
  })

  it('hits < limit and first page -> no cursors', async () => {
    const limit = 3
    const hits = [
      {
        _id: '1',
        _score: null,
        _source: { id: '1' },
        sort: ['2025-08-12T00:00:00.000Z', 'Alpha', '1'] as [string, string, string],
      },
      {
        _id: '2',
        _score: null,
        _source: { id: '2' },
        sort: ['2025-08-11T00:00:00.000Z', 'Bravo', '2'] as [string, string, string],
      },
    ]
    searchMock.mockResolvedValue({ body: { hits: { hits } } })
    const res: any = await handler(makeEvent({ qs: { limit } }), undefined as any)
    const body = JSON.parse(res.body)
    expect(body.nextPageCursor).toBeUndefined()
    expect(body.prevPageCursor).toBeUndefined()
  })

  it('prev request, middle page -> both cursors', async () => {
    const limit = 5
    const backwardHits = [
      {
        _id: 'l1',
        _score: null,
        _source: { id: 'l1' },
        sort: ['2025-08-01T00:00:00.000Z', 'Alpha', 'l1'] as [string, string, string],
      },
      {
        _id: 'l2',
        _score: null,
        _source: { id: 'l2' },
        sort: ['2025-08-02T00:00:00.000Z', 'Bravo', 'l2'] as [string, string, string],
      },
      {
        _id: 'l3',
        _score: null,
        _source: { id: 'l3' },
        sort: ['2025-08-03T00:00:00.000Z', 'Charlie', 'l3'] as [string, string, string],
      },
      {
        _id: 'l4',
        _score: null,
        _source: { id: 'l4' },
        sort: ['2025-08-04T00:00:00.000Z', 'Delta', 'l4'] as [string, string, string],
      },
      {
        _id: 'l5',
        _score: null,
        _source: { id: 'l5' },
        sort: ['2025-08-05T00:00:00.000Z', 'Echo', 'l5'] as [string, string, string],
      },
      {
        _id: 'l6',
        _score: null,
        _source: { id: 'l6' },
        sort: ['2025-08-06T00:00:00.000Z', 'Foxtrot', 'l6'] as [string, string, string],
      }, // <- extra
    ]
    const { __searchMock: searchMock } = jest.requireMock(
      '../../../../common/aws-sdk-clients/opensearch',
    ) as { __searchMock: jest.Mock }
    searchMock.mockResolvedValue({ body: { hits: { hits: backwardHits } } })

    const prevCursorIn = encodeCursor(['2025-08-07T00:00:00.000Z', 'Golf', 'l7'])
    const res: any = await handler(
      makeEvent({ qs: { limit, prevPageCursor: prevCursorIn } }) as any,
      undefined as any,
    )
    const body = JSON.parse(res.body)
    expect(body.count).toBe(limit)
    expect(body.nextPageCursor).toBeDefined()
    expect(body.prevPageCursor).toBeDefined()
  })

  it('prev request landing on first page -> next cursor only (no prev)', async () => {
    const limit = 5
    const backwardHits = [
      {
        _id: 'l1',
        _score: null,
        _source: { id: 'l1' },
        sort: ['2025-08-01T00:00:00.000Z', 'Alpha', 'l1'] as [string, string, string],
      },
      {
        _id: 'l2',
        _score: null,
        _source: { id: 'l2' },
        sort: ['2025-08-02T00:00:00.000Z', 'Bravo', 'l2'] as [string, string, string],
      },
      {
        _id: 'l3',
        _score: null,
        _source: { id: 'l3' },
        sort: ['2025-08-03T00:00:00.000Z', 'Charlie', 'l3'] as [string, string, string],
      },
      {
        _id: 'l4',
        _score: null,
        _source: { id: 'l4' },
        sort: ['2025-08-04T00:00:00.000Z', 'Delta', 'l4'] as [string, string, string],
      },
      {
        _id: 'l5',
        _score: null,
        _source: { id: 'l5' },
        sort: ['2025-08-05T00:00:00.000Z', 'Echo', 'l5'] as [string, string, string],
      },
    ]
    const { __searchMock: searchMock } = jest.requireMock(
      '../../../../common/aws-sdk-clients/opensearch',
    ) as { __searchMock: jest.Mock }
    searchMock.mockResolvedValue({ body: { hits: { hits: backwardHits } } })

    const prevCursorIn = encodeCursor(['2025-08-06T00:00:00.000Z', 'Foxtrot', 'l6'])
    const res: any = await handler(
      makeEvent({ qs: { limit, prevPageCursor: prevCursorIn } }) as any,
      undefined as any,
    )
    const body = JSON.parse(res.body)
    expect(body.count).toBe(5)
    expect(body.nextPageCursor).toBeDefined()
    expect(body.prevPageCursor).toBeUndefined()
  })
})
