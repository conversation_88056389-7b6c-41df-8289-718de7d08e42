```mermaid
flowchart TD
    %% External Actors
    Client[Client App]
    BlazeAPI[Blaze Canada API]
    
    %% AWS Services
    APIGW[API Gateway]
    S3Source[S3 Source Bucket<br/>CSV Files]
    S3Target[S3 Target Bucket<br/>Bulk Import Files]
    DDB[DynamoDB<br/>AO Records Table]
    SM[Secrets Manager<br/>Blaze API Key]
    EventBus[EventBridge<br/>Platform Events]
    
    %% Lambda Functions
    StartSync[λ start-sync-fn]
    EnableEvents[λ enable-events]
    IngestFile[λ ingest-file]
    ImportBatch[λ import-batch]
    
    %% Step Functions
    StateMachine[Step Functions<br/>State Machine]
    
    %% Phase Groupings
    subgraph "Phase 0: Initiation"
        direction TB
        Client --> APIGW
        APIGW --> StartSync
        StartSync --> SM
        StartSync --> BlazeAPI
        StartSync --> StateMachine
    end
    
    subgraph "Phase 1: Enable Events"
        direction TB
        StateMachine --> EnableEvents
        EnableEvents --> EventBus
    end
    
    subgraph "Phase 2: Parallel Processing"
        direction LR
        subgraph "Categories Flow"
            IngestCat[λ ingest-file<br/>Categories]
            ImportCat[λ import-batch<br/>Categories]
            IngestCat --> ImportCat
        end
        
        subgraph "Vendors Flow"
            IngestVen[λ ingest-file<br/>Vendors]
            ImportVen[λ import-batch<br/>Vendors]
            IngestVen --> ImportVen
        end
        
        subgraph "Brands Flow"
            IngestBra[λ ingest-file<br/>Brands]
            ImportBra[λ import-batch<br/>Brands]
            IngestBra --> ImportBra
        end
    end
    
    subgraph "Phase 3: Product Processing"
        direction TB
        IngestProd[λ ingest-file<br/>Products]
        BatchMap[Step Functions Map<br/>Product Batches]
        ImportProd[λ import-batch<br/>Products]
        
        IngestProd --> BatchMap
        BatchMap --> ImportProd
    end
    
    subgraph "Data Flow"
        direction TB
        S3Source --> IngestFile
        IngestFile --> DDB
        DDB --> ImportBatch
        ImportBatch --> S3Target
        ImportBatch --> BlazeAPI
    end
    
    %% Main Flow Connections
    EnableEvents --> IngestCat
    EnableEvents --> IngestVen
    EnableEvents --> IngestBra
    
    ImportCat --> IngestProd
    ImportVen --> IngestProd
    ImportBra --> IngestProd
    
    ImportProd --> Success[✅ Success State]
    
    %% Error Paths
    IngestFile -.-> Error2[❌ File Parse Error]
    ImportBatch -.-> Error3[❌ API Error]
    StateMachine -.-> Error4[❌ Timeout/Failure]
    
    %% Styling
    classDef lambda fill:#ff9900,stroke:#333,stroke-width:2px,color:#fff
    classDef aws fill:#232f3e,stroke:#333,stroke-width:2px,color:#fff
    classDef external fill:#4a90e2,stroke:#333,stroke-width:2px,color:#fff
    classDef success fill:#28a745,stroke:#333,stroke-width:2px,color:#fff
    classDef error fill:#dc3545,stroke:#333,stroke-width:2px,color:#fff
    
    class StartSync,EnableEvents,IngestFile,ImportBatch,IngestCat,ImportCat,IngestVen,ImportVen,IngestBra,ImportBra,IngestProd,ImportProd lambda
    class APIGW,S3Source,S3Target,DDB,SM,EventBus,StateMachine,BatchMap aws
    class Client,BlazeAPI external
    class Success success
    class Error1,Error2,Error3,Error4 error